// ==UserScript==
// @name         斗鱼全屏按钮
// @name:en      Douyu Fullscreen Button
// @namespace    http://tampermonkey.net/
// @version      3.2.0
// @description  为斗鱼直播间添加双击播放器全屏功能，自动选择最高画质，5秒后自动全屏，全屏保护机制防止其他脚本强制退出全屏，提供简洁的全屏体验（仅支持PC端）
// @description:en Add double-click fullscreen functionality to Douyu live rooms, auto highest quality selection, and auto fullscreen after 5 seconds (PC only)
// <AUTHOR>
// @match        https://www.douyu.com/*
// @match        https://douyu.com/*
// @icon         data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iMTIiIGZpbGw9IiMzMzMiLz4KPHN2ZyB4PSIxNiIgeT0iMTYiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIj4KPHN0cm9rZSBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Im04IDNhMSAxIDAgMCAwLTEgMXY0YTEgMSAwIDAgMCAxIDFoNGExIDEgMCAwIDAgMS0xVjRhMSAxIDAgMCAwLTEtMXoiLz4KPHN0cm9rZSBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Im0xNSAzaDRhMSAxIDAgMCAxIDEgMXY0Ii8+CjxwYXRoIGQ9Im0yMSAxNXY0YTEgMSAwIDAgMS0xIDFoLTQiLz4KPHN0cm9rZSBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Im05IDIxSDVhMSAxIDAgMCAxLTEtMXYtNCIvPgo8cGF0aCBkPSJtMyA5VjVhMSAxIDAgMCAxIDEtMWg0Ii8+Cjwvc3Ryb2tlPgo8L3N2Zz4KPC9zdmc+
// @license      MIT
// @grant        none
// @run-at       document-end
// @noframes
// ==/UserScript==


(function() {
    'use strict';

    // 检测是否在直播间页面
    const isInLiveRoom = () => {
        const url = window.location.href;
        // 匹配斗鱼直播间URL格式：www.douyu.com/数字 或 douyu.com/数字
        const liveRoomPattern = /douyu\.com\/(\d+)(?:\?|$|\/)/;
        return liveRoomPattern.test(url);
    };

    // 自动切换最高画质功能
    const switchToHighestQuality = () => {
        try
        {
            // 查找画质选择器
            const qualitySelector = document.querySelector('.c5-6a3710[value="画质 "]');
            if (!qualitySelector)
            {
                console.log("来自**斗鱼全屏按钮**: 未找到画质选择器");
                return;
            }

            // 查找画质列表
            const qualityList = qualitySelector.nextElementSibling;
            if (!qualityList || qualityList.tagName !== 'UL')
            {
                console.log("来自**斗鱼全屏按钮**: 未找到画质列表");
                return;
            }

            // 获取第一个画质选项（通常是最高画质）
            const firstQualityOption = qualityList.querySelector('li:first-child');
            if (!firstQualityOption)
            {
                console.log("来自**斗鱼全屏按钮**: 未找到画质选项");
                return;
            }

            // 检查是否已经是最高画质
            if (firstQualityOption.classList.contains('selected-3a8039'))
            {
                console.log("来自**斗鱼全屏按钮**: 已经是最高画质");
                return;
            }

            // 点击切换到最高画质
            firstQualityOption.click();
            console.log("来自**斗鱼全屏按钮**: 已切换到最高画质");
        } catch (error)
        {
            console.error('来自**斗鱼全屏按钮**: 切换画质失败:', error);
        }
    };



    // 全屏功能
    const switchFullMode = () => {
        if (!document.fullscreenElement)
        {
            const videoContainer = document.querySelector("#js-player-video-case");
            if (videoContainer)
            {
                videoContainer.requestFullscreen()
                    .then(() => {
                        // 启用全屏保护
                        enableFullscreenProtection();
                    })
                    .catch(err => {
                        console.error('来自**斗鱼全屏按钮**: 全屏请求失败:', err);
                    });
            }
        } else
        {
            // 用户主动退出全屏，先禁用保护
            disableFullscreenProtection();
            document.exitFullscreen();
        }
    };

    // 执行播放器全屏
    const enterFullscreen = () => {
        const videoContainer = document.querySelector("#js-player-video-case");
        if (videoContainer)
        {
            videoContainer.requestFullscreen()
                .then(() => {
                    console.log("来自**斗鱼全屏按钮**: 自动全屏成功");
                    // 启用全屏保护
                    enableFullscreenProtection();
                })
                .catch(err => {
                    console.error('来自**斗鱼全屏按钮**: 自动全屏失败:', err);
                });
        } else
        {
            console.log("来自**斗鱼全屏按钮**: 未找到播放器容器");
        }
    };

    // 全屏保护机制
    let fullscreenProtectionEnabled = false;
    let originalExitFullscreen = null;

    const enableFullscreenProtection = () => {
        if (fullscreenProtectionEnabled) return;

        console.log("来自**斗鱼全屏按钮**: 启用全屏保护机制");
        fullscreenProtectionEnabled = true;

        // 备份原始的退出全屏函数
        originalExitFullscreen = document.exitFullscreen;

        // 重写 document.exitFullscreen 函数
        document.exitFullscreen = function() {
            console.log("来自**斗鱼全屏按钮**: 拦截到退出全屏调用，已阻止");
            // 返回一个resolved的Promise，让调用者以为成功了
            return Promise.resolve();
        };

        // 监听ESC键，允许用户手动退出
        document.addEventListener('keydown', handleEscKey);
    };

    const disableFullscreenProtection = () => {
        if (!fullscreenProtectionEnabled) return;

        console.log("来自**斗鱼全屏按钮**: 禁用全屏保护机制");
        fullscreenProtectionEnabled = false;

        // 恢复原始的退出全屏函数
        if (originalExitFullscreen)
        {
            document.exitFullscreen = originalExitFullscreen;
        }

        document.removeEventListener('keydown', handleEscKey);
    };

    const handleEscKey = (event) => {
        if (event.key === 'Escape' && document.fullscreenElement)
        {
            console.log("来自**斗鱼全屏按钮**: 用户按ESC键退出全屏");
            disableFullscreenProtection();
            // 调用原始的退出全屏函数
            if (originalExitFullscreen)
            {
                originalExitFullscreen.call(document);
            }
        }
    };

    // 添加双击播放器全屏功能
    const addDoubleClickFullscreen = () => {
        const playerVideo = document.querySelector("#js-player-video");
        const videoElement = document.querySelector("#js-player-video video");

        if (playerVideo)
        {
            // 为播放器容器添加双击事件
            playerVideo.addEventListener('dblclick', (event) => {
                // 防止事件冒泡
                event.preventDefault();
                event.stopPropagation();

                switchFullMode();
                console.log("来自**斗鱼全屏按钮**: 双击播放器触发全屏");
            });

            console.log("来自**斗鱼全屏按钮**: 双击全屏功能已启用");
        }

        // 如果找到video元素，也为其添加双击事件（备用方案）
        if (videoElement)
        {
            videoElement.addEventListener('dblclick', (event) => {
                event.preventDefault();
                event.stopPropagation();

                switchFullMode();
                console.log("来自**斗鱼全屏按钮**: 双击视频元素触发全屏");
            });
        }
    };





    // 自动全屏状态管理
    let autoFullscreenPending = false;
    let autoFullscreenExecuted = false;

    // 显示全屏提示按钮
    const showFullscreenPrompt = () => {
        if (!isInLiveRoom()) return;

        // 创建提示元素
        const promptDiv = document.createElement('div');
        promptDiv.id = 'douyu-fullscreen-prompt';
        promptDiv.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 10000;
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                transition: all 0.3s ease;
                border: 2px solid #ff6600;
            ">
                🎬 点击进入全屏观看
                <div style="font-size: 12px; margin-top: 5px; opacity: 0.8;">
                    (5秒后自动消失)
                </div>
            </div>
        `;

        // 添加点击事件
        promptDiv.addEventListener('click', () => {
            console.log("来自**斗鱼全屏按钮**: 用户点击全屏提示");

            // 自动切换到最高画质
            switchToHighestQuality();

            // 执行全屏
            setTimeout(() => {
                enterFullscreen();
            }, 500);

            // 移除提示
            promptDiv.remove();
        });

        // 添加到页面
        document.body.appendChild(promptDiv);

        // 5秒后自动移除提示
        setTimeout(() => {
            if (promptDiv.parentNode)
            {
                promptDiv.remove();
            }
        }, 5000);
    };

    // 等待用户交互后执行自动全屏
    const setupAutoFullscreenOnUserInteraction = () => {
        if (!isInLiveRoom())
        {
            console.log("来自**斗鱼全屏按钮**: 非直播间页面，跳过自动全屏设置");
            return;
        }

        console.log("来自**斗鱼全屏按钮**: 设置自动全屏，等待用户交互");
        autoFullscreenPending = true;

        // 监听各种用户交互事件
        const userInteractionEvents = ['click', 'keydown', 'mousemove', 'scroll', 'touchstart'];

        const handleUserInteraction = () => {
            if (!autoFullscreenPending || autoFullscreenExecuted) return;

            console.log("来自**斗鱼全屏按钮**: 检测到用户交互，准备执行自动全屏");
            autoFullscreenExecuted = true;

            // 移除所有事件监听器
            userInteractionEvents.forEach(eventType => {
                document.removeEventListener(eventType, handleUserInteraction, true);
            });

            // 延迟一小段时间执行，确保页面完全加载
            setTimeout(() => {
                console.log("来自**斗鱼全屏按钮**: 检测到直播间页面");

                // 自动切换到最高画质
                switchToHighestQuality();

                // 执行自动全屏
                console.log("执行自动全屏");
                enterFullscreen();
            }, 1000);
        };

        // 添加事件监听器（使用捕获阶段确保能够捕获到事件）
        userInteractionEvents.forEach(eventType => {
            document.addEventListener(eventType, handleUserInteraction, true);
        });

        // 设置超时，如果用户长时间没有交互，则取消自动全屏
        setTimeout(() => {
            if (autoFullscreenPending && !autoFullscreenExecuted)
            {
                console.log("来自**斗鱼全屏按钮**: 用户长时间无交互，取消自动全屏");
                autoFullscreenPending = false;

                // 移除事件监听器
                userInteractionEvents.forEach(eventType => {
                    document.removeEventListener(eventType, handleUserInteraction, true);
                });
            }
        }, 30000); // 30秒超时
    };

    // 主函数
    const init = () => {
        // 添加双击播放器全屏功能
        addDoubleClickFullscreen();

        // 等待页面加载完成后设置自动全屏
        setTimeout(() => {
            // 方案1：等待用户交互后自动全屏（推荐）
            setupAutoFullscreenOnUserInteraction();

            // 方案2：显示提示按钮（备选方案，如需启用请取消注释）
            // showFullscreenPrompt();
        }, 2000); // 延迟2秒确保页面元素加载完成
    };

    // 启动
    init();
})();
